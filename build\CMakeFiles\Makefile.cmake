# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCXXInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeGenericSystem.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeInitializeConfigs.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeLanguageInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeRCInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Compiler/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/GNU.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-C.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-C.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-GNU.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-Initialize.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows-windres.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/Windows.cmake"
  "C:/Program Files/CMake/share/cmake-4.1/Modules/Platform/WindowsPaths.cmake"
  "D:/pet/CODE/图形/HHMM/Hw3/CMakeLists.txt"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/Rasterizer.dir/DependInfo.cmake"
  )
