{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.10"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "<PERSON>ster<PERSON>", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "Rasterizer::@6890427a1f51a3e7e1df", "jsonFile": "target-Rasterizer-Debug-d77f806f91dab8713766.json", "name": "<PERSON>ster<PERSON>", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "D:/pet/CODE/图形/build", "source": "D:/pet/CODE/图形/HHMM/Hw3"}, "version": {"major": 2, "minor": 8}}