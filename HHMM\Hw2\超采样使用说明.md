# 超采样抗锯齿使用说明

## 🔧 如何切换渲染模式

### 步骤 1：打开 main.cpp 文件

找到第 15 行左右的代码：

```cpp
// 手动设置是否启用超采样 - 直接修改这里的值
bool use_super_sampling = true;  // 改为 false 可关闭超采样
```

### 步骤 2：修改设置

- **启用超采样**: `bool use_super_sampling = true;`
- **关闭超采样**: `bool use_super_sampling = false;`

### 步骤 3：重新编译运行

```powershell
# 重新编译
cmake --build . --config Release

# 运行程序
Release\Rasterizer.exe
```

## 📊 效果对比

### 普通渲染 (use_super_sampling = false)

- 渲染速度快
- 三角形边缘可能有锯齿
- 内存占用少

### 超采样渲染 (use_super_sampling = true)

- 渲染速度较慢（约 4 倍计算量）
- 三角形边缘平滑，锯齿明显减少
- 内存占用增加（每像素 4 个样本）

## 🎯 技术原理

### 2x2 超采样工作流程

1. **样本生成**: 每个像素内生成 4 个采样点
2. **独立测试**: 每个样本独立进行三角形内部测试
3. **独立深度**: 每个样本维护自己的深度值
4. **颜色混合**: 4 个样本颜色取平均值

### 采样点位置

```
像素内采样点分布：
+-------+-------+
| 0.25  | 0.75  |  ← y=0.25
| 0.25  | 0.25  |
+-------+-------+
| 0.25  | 0.75  |  ← y=0.75
| 0.75  | 0.75  |
+-------+-------+
```

## 💡 使用建议

1. **开发调试**: 使用普通渲染，速度快
2. **最终效果**: 使用超采样渲染，质量高
3. **性能测试**: 可以对比两种模式的性能差异
