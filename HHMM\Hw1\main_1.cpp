#include "Triangle.hpp"
#include "rasterizer.hpp"
#include <Eigen/Eigen>
#include <iostream>
#include <opencv2/opencv.hpp>

constexpr double MY_PI = 3.1415926;

Eigen::Matrix4f get_view_matrix(Eigen::Vector3f eye_pos)
{
    Eigen::Matrix4f view = Eigen::Matrix4f::Identity();

    Eigen::Matrix4f translate;
    translate << 1, 0, 0, -eye_pos[0], 0, 1, 0, -eye_pos[1], 0, 0, 1,
        -eye_pos[2], 0, 0, 0, 1;

    view = translate * view;

    return view;
}

Eigen::Matrix4f get_model_matrix(float rotation_angle)
{
    Eigen::Matrix4f model = Eigen::Matrix4f::Identity();

    // TODO: Implement this function
    // Create the model matrix for rotating the triangle around the Z axis.
    // Then return it.
    // 创建用于绕Z轴旋转三角形的模型矩阵。
    // 然后返回它。
	float angle_rad = rotation_angle * MY_PI / 180.0f;

    model << cos(angle_rad), -sin(angle_rad), 0, 0,
             sin(angle_rad),  cos(angle_rad), 0, 0,
             0,               0,              1, 0,
             0,               0,              0, 1;

    return model;
}

Eigen::Matrix4f get_projection_matrix(float eye_fov, float aspect_ratio,
                                      float zNear, float zFar)
{
    // Students will implement this function
    // 学生将实现此函数

    Eigen::Matrix4f projection = Eigen::Matrix4f::Identity();

    // TODO: Implement this function
    // 创建用于给定参数的投影矩阵。
    // 然后返回它。
	Eigen::Matrix4f persp_to_ortho;
    persp_to_ortho << zNear, 0, 0, 0,
                      0, zNear, 0, 0,
                      0, 0, zNear + zFar, -zNear * zFar,
                      0, 0, 1, 0;
	
	Eigen::Matrix4f ortho;
	Eigen::Matrix4f scale;
	Eigen::Matrix4f translate;

	float eye_fov_rad = eye_fov * MY_PI / 180.0f;
	float top = abs(zNear) * tan(eye_fov_rad / 2.0f);
	float bottom = -top;
	float right = top * aspect_ratio;
	float left = -right;

    translate << 1, 0, 0, -(left + right) / 2,
                      0, 1, 0, -(top + bottom) / 2,
                      0, 0, 1, -(zNear + zFar) / 2,
		0, 0, 0, 1;
    scale << 2 / (right - left), 0, 0, 0,
             0, 2 / (top - bottom), 0, 0,
             0, 0, 2 / (zNear - zFar), 0,
		0, 0, 0, 1;
	ortho = scale * translate;
	projection = ortho * persp_to_ortho;

    return projection;
}

Eigen::Matrix4f get_rotation(Eigen::Vector3f axis, float angle)
{
	Eigen::Matrix4f rotation = Eigen::Matrix4f::Identity();

	Eigen::Matrix3f R;
	Eigen::Matrix3f I = Eigen::Matrix3f::Identity();
    Eigen::Matrix3f K;

    float angle_rad = angle * MY_PI / 180.0f;

    K << 0, -axis.z(), axis.y(),
        axis.z(), 0, -axis.x(),
        -axis.y(), axis.x(), 0;

    R = I * cos(angle_rad) +
        (1 - cos(angle_rad)) * (axis * axis.transpose()) +
        sin(angle_rad) * K;

	rotation.block<3, 3>(0, 0) = R;

	return rotation;
}


int main(int argc, const char** argv)
{
    float angle = 0;
    bool command_line = false;
    std::string filename = "output.png";

    if (argc >= 3) {
        command_line = true;
        angle = std::stof(argv[2]); // -r by default
        // 默认是-r
        if (argc == 4) {
            filename = std::string(argv[3]);
        }
    }

    rst::rasterizer r(700, 700);

    Eigen::Vector3f eye_pos = {0, 0, 5};

    std::vector<Eigen::Vector3f> pos{{2, 0, -2}, {0, 2, -2}, {-2, 0, -2}};

    std::vector<Eigen::Vector3i> ind{{0, 1, 2}};

    auto pos_id = r.load_positions(pos);
    auto ind_id = r.load_indices(ind);

    int key = 0;
    int frame_count = 0;

    if (command_line) {
        r.clear(rst::Buffers::Color | rst::Buffers::Depth);

        r.set_model(get_model_matrix(angle));
        r.set_view(get_view_matrix(eye_pos));
        r.set_projection(get_projection_matrix(45, 1, -0.1, -50));

        r.draw(pos_id, ind_id, rst::Primitive::Triangle);
        cv::Mat image(700, 700, CV_32FC3, r.frame_buffer().data());
        image.convertTo(image, CV_8UC3, 1.0f);

        cv::imwrite(filename, image);

        return 0;
    }

    while (key != 27) {
        r.clear(rst::Buffers::Color | rst::Buffers::Depth);

        //r.set_model(get_model_matrix(angle));
		r.set_model(get_rotation(Eigen::Vector3f(0, 1, 0), angle));
        r.set_view(get_view_matrix(eye_pos));
        r.set_projection(get_projection_matrix(45, 1, -0.1, -50));
		//r.set_pixel(Eigen::Vector2f(0, 0), Eigen::Vector3f(0, 0, 0));

        r.draw(pos_id, ind_id, rst::Primitive::Triangle);

        cv::Mat image(700, 700, CV_32FC3, r.frame_buffer().data());
        image.convertTo(image, CV_8UC3, 1.0f);
        cv::imshow("image", image);
        key = cv::waitKey(10);

        std::cout << "frame count: " << frame_count++ << '\n';
        // 帧计数

        if (key == 'a') {
            angle += 10;
        }
        else if (key == 'd') {
            angle -= 10;
        }
    }

    return 0;
}
