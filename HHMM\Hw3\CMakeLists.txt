cmake_minimum_required(VERSION 3.10)
project(Rasterizer)

set(CMAKE_CXX_STANDARD 17)

# Set paths based on your screenshots
set(OPENCV_ROOT "E:/opencv")
set(EIGEN3_INCLUDE_DIR "E:/eigen-3.4.0")

# Try to use the prebuilt OpenCV binaries you showed in the screenshot
set(OpenCV_INCLUDE_DIRS "${OPENCV_ROOT}/build/include")
set(OpenCV_BIN_DIR "${OPENCV_ROOT}/build/x64/vc16/bin")
set(OpenCV_LIB_DIR "${OPENCV_ROOT}/build/x64/vc16/lib")

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${EIGEN3_INCLUDE_DIR})

# Manually specify OpenCV libraries (common ones for basic functionality)
set(OpenCV_LIBS
    "${OpenCV_LIB_DIR}/opencv_world4120.lib"
)

# Check if debug version exists and add it
if(EXISTS "${OpenCV_LIB_DIR}/opencv_world4120d.lib")
    list(APPEND OpenCV_LIBS "${OpenCV_LIB_DIR}/opencv_world4120d.lib")
endif()

message(STATUS "OpenCV include: ${OpenCV_INCLUDE_DIRS}")
message(STATUS "OpenCV bin: ${OpenCV_BIN_DIR}")
message(STATUS "OpenCV lib: ${OpenCV_LIB_DIR}")
message(STATUS "Eigen3 include: ${EIGEN3_INCLUDE_DIR}")

# Create executable
add_executable(Rasterizer
    main.cpp
    rasterizer.hpp
    rasterizer.cpp
    global.hpp
    Triangle.hpp
    Triangle.cpp
    Texture.hpp
    Texture.cpp
    Shader.hpp
    OBJ_Loader.h
)

# Link libraries
target_link_libraries(Rasterizer ${OpenCV_LIBS})

# Optional: Add compile options for debugging
# target_compile_options(Rasterizer PUBLIC -Wall -Wextra -pedantic)