cmake_minimum_required(VERSION 3.10)
project(Rasterizer)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Set OpenCV and Eigen paths
set(OpenCV_DIR "E:/opencv/build")
set(EIGEN3_INCLUDE_DIR "E:/eigen-3.4.0")

# Find OpenCV (prefer user-provided OpenCV_DIR; otherwise search defaults)
if(DEFINED OpenCV_DIR)
    find_package(OpenCV REQUIRED PATHS ${OpenCV_DIR} NO_DEFAULT_PATH)
else()
    find_package(OpenCV REQUIRED)
endif()

# Include directories
include_directories(${OpenCV_INCLUDE_DIRS})
include_directories(${EIGEN3_INCLUDE_DIR})

add_executable(Rasterizer main.cpp rasterizer.hpp rasterizer.cpp global.hpp Triangle.hpp Triangle.cpp Texture.hpp Texture.cpp Shader.hpp OBJ_Loader.h)

# Link libraries
target_link_libraries(Rasterizer ${OpenCV_LIBRARIES})

#target_compile_options(Rasterizer PUBLIC -Wall -Wextra -pedantic)