# 快速开始指南

## 🚀 一键编译运行

### 方法一：使用批处理文件（推荐）

```powershell
# 双击运行
build.bat
```

### 方法二：手动编译

```powershell
# 1. 创建构建目录
mkdir build
cd build

# 2. 生成构建文件
cmake ..

# 3. 编译项目
cmake --build . --config Release

# 4. 运行程序
Release\Rasterizer.exe
```

## 📋 前置条件检查

在编译前，请确保：

1. **已安装 CMake** (版本 3.10+)
2. **已安装 OpenCV** 并配置路径
3. **已安装 Eigen3** 并配置路径
4. **已安装 Visual Studio** 或 **MinGW**

## ⚙️ 路径配置

编辑 `CMakeLists.txt` 文件，修改以下路径：

```cmake
# 修改为你的实际OpenCV安装路径
set(OpenCV_DIR "E:/OpenCV/opencv/build")

# 修改为你的实际Eigen3安装路径
set(EIGEN3_INCLUDE_DIR "E:/eigen-3.4.0")
```

## 🔧 常见问题

### 问题 1：找不到 OpenCV

**解决**：检查 CMakeLists.txt 中的 OpenCV_DIR 路径是否正确

### 问题 2：找不到 Eigen3

**解决**：检查 CMakeLists.txt 中的 EIGEN3_INCLUDE_DIR 路径是否正确

### 问题 3：编译错误

**解决**：确保使用支持 C++17 的编译器

## 📁 项目结构

```
Hw2/
├── main.cpp              # 主程序
├── rasterizer.cpp        # 光栅化器实现
├── Triangle.cpp          # 三角形类
├── CMakeLists.txt        # 构建配置
├── build.bat            # 一键编译脚本
└── 编译运行指南.md        # 详细文档
```

## 🎯 预期输出

程序运行后会显示一个窗口，渲染两个不同颜色的三角形。

### 控制说明
- `ESC` - 退出程序

### 🎨 超采样功能设置

在 `main.cpp` 中找到这行代码：
```cpp
bool use_super_sampling = true;  // 改为 false 可关闭超采样
```

**修改方法**：
- 设置为 `true` - 启用2x2超采样抗锯齿
- 设置为 `false` - 使用普通渲染

### 超采样技术说明
- **2x2超采样**: 每个像素进行4次采样，有效减少锯齿效果
- **采样位置**: 在像素内的(0.25,0.25), (0.75,0.25), (0.25,0.75), (0.75,0.75)位置
- **独立深度缓冲**: 每个样本都有独立的深度值，确保正确的深度测试
- **颜色混合**: 4个样本的颜色取平均值作为最终像素颜色
