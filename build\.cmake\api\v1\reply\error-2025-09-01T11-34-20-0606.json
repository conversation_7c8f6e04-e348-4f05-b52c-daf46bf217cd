{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "C:/Program Files/CMake/bin/cmake.exe", "cpack": "C:/Program Files/CMake/bin/cpack.exe", "ctest": "C:/Program Files/CMake/bin/ctest.exe", "root": "C:/Program Files/CMake/share/cmake-4.1"}, "version": {"isDirty": false, "major": 4, "minor": 1, "patch": 0, "string": "4.1.0", "suffix": ""}}, "objects": [], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}, {"error": "no buildsystem generated"}]}}}}