{"artifacts": [{"path": "Rasterizer.exe"}, {"path": "Rasterizer.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "target_link_libraries", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 35, "parent": 0}, {"command": 1, "file": 0, "line": 49, "parent": 0}, {"command": 2, "file": 0, "line": 16, "parent": 0}, {"command": 2, "file": 0, "line": 17, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "includes": [{"backtrace": 3, "path": "E:/opencv/build/include"}, {"backtrace": 4, "path": "E:/eigen-3.4.0"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 2, 5, 7]}], "id": "Rasterizer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"backtrace": 2, "fragment": "E:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4120.lib", "role": "libraries"}, {"backtrace": 2, "fragment": "E:\\opencv\\build\\x64\\vc16\\lib\\opencv_world4120d.lib", "role": "libraries"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "<PERSON>ster<PERSON>", "nameOnDisk": "Rasterizer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 5, 7]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 6, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "rasterizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "rasterizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "global.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Triangle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Triangle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Texture.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Texture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Shader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "OBJ_Loader.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}