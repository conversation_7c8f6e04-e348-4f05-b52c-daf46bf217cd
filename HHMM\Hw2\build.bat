@echo off
echo 开始编译图形学作业...

REM 检查是否存在build目录，如果不存在则创建
if not exist "build" mkdir build

REM 进入build目录
cd build

REM 运行CMake生成构建文件
echo 正在生成构建文件...
cmake ..

REM 检查CMake是否成功
if %ERRORLEVEL% neq 0 (
    echo CMake配置失败！请检查依赖库路径配置。
    pause
    exit /b 1
)

REM 编译项目
echo 正在编译项目...
cmake --build . --config Release

REM 检查编译是否成功
if %ERRORLEVEL% neq 0 (
    echo 编译失败！请检查代码错误。
    pause
    exit /b 1
)

echo 编译成功！
echo 可执行文件位置: build\Release\Rasterizer.exe

REM 询问是否运行程序
set /p run="是否现在运行程序？(y/n): "
if /i "%run%"=="y" (
    echo 正在运行程序...
    Release\Rasterizer.exe
)

pause 