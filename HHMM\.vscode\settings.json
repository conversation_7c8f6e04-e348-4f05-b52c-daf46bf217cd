{"cmake.configureOnOpen": true, "cmake.buildDirectory": "${workspaceFolder}/Hw2/build", "cmake.sourceDirectory": "${workspaceFolder}/Hw2", "C_Cpp.default.configurationProvider": "ms-vscode.cmake-tools", "C_Cpp.default.includePath": ["${workspaceFolder}/**", "E:/eigen-3.4.0", "E:/opencv/build/include"], "C_Cpp.default.compilerPath": "E:/VS/VC/Tools/MSVC/14.44.35207/bin/Hostx64/x64/cl.exe", "C_Cpp.default.cppStandard": "c++17", "C_Cpp.default.intelliSenseMode": "windows-msvc-x64"}