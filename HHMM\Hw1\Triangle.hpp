//
// Created by LEI XU on 4/11/19.
// 创建者：LEI XU，日期：2019年4月11日。

#ifndef RASTERIZER_TRIANGLE_H
#define RASTERIZER_TRIANGLE_H

#include <Eigen/Eigen>

using namespace Eigen;
class Triangle
{
  public:
    Vector3f v[3]; /*the original coordinates of the triangle, v0, v1, v2 in
                      counter clockwise order*/
    /*Per vertex values*/
    // color at each vertex;
    // 每个顶点的颜色；
    Vector3f color[3];
    // texture u,v
    // 纹理坐标u,v
    Vector2f tex_coords[3];
    // normal vector for each vertex
    // 每个顶点的法向量
    Vector3f normal[3];

    // Texture *tex;
    Triangle();

    Eigen::Vector3f a() const { return v[0]; }
    Eigen::Vector3f b() const { return v[1]; }
    Eigen::Vector3f c() const { return v[2]; }

    void setVertex(int ind, Vector3f ver); /*set i-th vertex coordinates */
    // set i-th vertex coordinates
    // 设置第i个顶点坐标
    void setNormal(int ind, Vector3f n);   /*set i-th vertex normal vector*/
    // set i-th vertex normal vector
    // 设置第i个顶点法向量
    void setColor(int ind, float r, float g, float b); /*set i-th vertex color*/
    // set i-th vertex color
    // 设置第i个顶点颜色
    void setTexCoord(int ind, float s,
                     float t); /*set i-th vertex texture coordinate*/
    // set i-th vertex texture coordinate
    // 设置第i个顶点纹理坐标
    std::array<Vector4f, 3> toVector4() const;
};

#endif // RASTERIZER_TRIANGLE_H
// 头文件结束标志
