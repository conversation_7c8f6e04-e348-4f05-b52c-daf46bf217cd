{"artifacts": [{"path": "Rasterizer.exe"}, {"path": "Rasterizer.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 29, "parent": 0}, {"command": 1, "file": 0, "line": 27, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++17"}], "includes": [{"backtrace": 2, "path": "E:/eigen-3.4.0"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "17"}, "sourceIndexes": [0, 2, 5, 7]}], "id": "Rasterizer::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "<PERSON>ster<PERSON>", "nameOnDisk": "Rasterizer.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 5, 7]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1, 3, 4, 6, 8, 9]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "rasterizer.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "rasterizer.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "global.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "Triangle.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Triangle.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Texture.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "Texture.cpp", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "Shader.hpp", "sourceGroupIndex": 1}, {"backtrace": 1, "path": "OBJ_Loader.h", "sourceGroupIndex": 1}], "type": "EXECUTABLE"}