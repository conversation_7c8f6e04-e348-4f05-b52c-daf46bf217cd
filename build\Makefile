# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = "C:\Program Files\CMake\bin\cmake.exe"

# The command to remove a file.
RM = "C:\Program Files\CMake\bin\cmake.exe" -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = D:\pet\CODE\图形\HHMM\Hw3

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = D:\pet\CODE\图形\build

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	"C:\Program Files\CMake\bin\cmake-gui.exe" -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	"C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start D:\pet\CODE\图形\build\CMakeFiles D:\pet\CODE\图形\build\\CMakeFiles\progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start D:\pet\CODE\图形\build\CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named Rasterizer

# Build rule for target.
Rasterizer: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Makefile2 Rasterizer
.PHONY : Rasterizer

# fast build rule for target.
Rasterizer/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/build
.PHONY : Rasterizer/fast

Texture.obj: Texture.cpp.obj
.PHONY : Texture.obj

# target to build an object file
Texture.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/Texture.cpp.obj
.PHONY : Texture.cpp.obj

Texture.i: Texture.cpp.i
.PHONY : Texture.i

# target to preprocess a source file
Texture.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/Texture.cpp.i
.PHONY : Texture.cpp.i

Texture.s: Texture.cpp.s
.PHONY : Texture.s

# target to generate assembly for a file
Texture.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/Texture.cpp.s
.PHONY : Texture.cpp.s

Triangle.obj: Triangle.cpp.obj
.PHONY : Triangle.obj

# target to build an object file
Triangle.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/Triangle.cpp.obj
.PHONY : Triangle.cpp.obj

Triangle.i: Triangle.cpp.i
.PHONY : Triangle.i

# target to preprocess a source file
Triangle.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/Triangle.cpp.i
.PHONY : Triangle.cpp.i

Triangle.s: Triangle.cpp.s
.PHONY : Triangle.s

# target to generate assembly for a file
Triangle.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/Triangle.cpp.s
.PHONY : Triangle.cpp.s

main.obj: main.cpp.obj
.PHONY : main.obj

# target to build an object file
main.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/main.cpp.obj
.PHONY : main.cpp.obj

main.i: main.cpp.i
.PHONY : main.i

# target to preprocess a source file
main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/main.cpp.i
.PHONY : main.cpp.i

main.s: main.cpp.s
.PHONY : main.s

# target to generate assembly for a file
main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/main.cpp.s
.PHONY : main.cpp.s

rasterizer.obj: rasterizer.cpp.obj
.PHONY : rasterizer.obj

# target to build an object file
rasterizer.cpp.obj:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/rasterizer.cpp.obj
.PHONY : rasterizer.cpp.obj

rasterizer.i: rasterizer.cpp.i
.PHONY : rasterizer.i

# target to preprocess a source file
rasterizer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/rasterizer.cpp.i
.PHONY : rasterizer.cpp.i

rasterizer.s: rasterizer.cpp.s
.PHONY : rasterizer.s

# target to generate assembly for a file
rasterizer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles\Rasterizer.dir\build.make CMakeFiles/Rasterizer.dir/rasterizer.cpp.s
.PHONY : rasterizer.cpp.s

# Help Target
help:
	@echo The following are some of the valid targets for this Makefile:
	@echo ... all (the default if no target is provided)
	@echo ... clean
	@echo ... depend
	@echo ... edit_cache
	@echo ... rebuild_cache
	@echo ... Rasterizer
	@echo ... Texture.obj
	@echo ... Texture.i
	@echo ... Texture.s
	@echo ... Triangle.obj
	@echo ... Triangle.i
	@echo ... Triangle.s
	@echo ... main.obj
	@echo ... main.i
	@echo ... main.s
	@echo ... rasterizer.obj
	@echo ... rasterizer.i
	@echo ... rasterizer.s
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles\Makefile.cmake 0
.PHONY : cmake_check_build_system

