﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{FD44D1DD-7E07-3A70-B6B1-47E7158FC324}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>Rasterizer</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Rasterizer.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Rasterizer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Rasterizer.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Rasterizer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Rasterizer.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Rasterizer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Rasterizer.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Rasterizer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/OpenCV/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120d.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/PETlab/图形/hw/HHMM/Hw2/build/Debug/Rasterizer.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/PETlab/图形/hw/HHMM/Hw2/build/Debug/Rasterizer.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/OpenCV/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/PETlab/图形/hw/HHMM/Hw2/build/Release/Rasterizer.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/PETlab/图形/hw/HHMM/Hw2/build/Release/Rasterizer.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/OpenCV/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/PETlab/图形/hw/HHMM/Hw2/build/MinSizeRel/Rasterizer.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/PETlab/图形/hw/HHMM/Hw2/build/MinSizeRel/Rasterizer.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "E:/OpenCV/opencv/build/include"</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>Default</BasicRuntimeChecks>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <MinimalRebuild></MinimalRebuild>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <SupportJustMyCode></SupportJustMyCode>
      <UseFullPaths>false</UseFullPaths>
      <WarningLevel>Level3</WarningLevel>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>E:\eigen-3.4.0;E:\OpenCV\opencv\build\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <Link>
      <AdditionalDependencies>E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;E:\OpenCV\opencv\build\x64\vc16\lib\opencv_world4120.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>E:/PETlab/图形/hw/HHMM/Hw2/build/RelWithDebInfo/Rasterizer.lib</ImportLibrary>
      <ProgramDataBaseFile>E:/PETlab/图形/hw/HHMM/Hw2/build/RelWithDebInfo/Rasterizer.pdb</ProgramDataBaseFile>
      <SubSystem>Console</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="E:\PETlab\图形\hw\HHMM\Hw2\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule E:/PETlab/图形/hw/HHMM/Hw2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-file E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule E:/PETlab/图形/hw/HHMM/Hw2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-file E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule E:/PETlab/图形/hw/HHMM/Hw2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-file E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule E:/PETlab/图形/hw/HHMM/Hw2/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
E:\Cmake\bin\cmake.exe -SE:/PETlab/图形/hw/HHMM/Hw2 -BE:/PETlab/图形/hw/HHMM/Hw2/build --check-stamp-file E:/PETlab/图形/hw/HHMM/Hw2/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\Cmake\share\cmake-3.30\Modules\CMakeCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCXXInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeCommonLanguageInclude.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeGenericSystem.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeInitializeConfigs.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeLanguageInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeRCInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInformation.cmake;E:\Cmake\share\cmake-3.30\Modules\CMakeSystemSpecificInitialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\CMakeCommonCompilerMacros.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Compiler\MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageHandleStandardArgs.cmake;E:\Cmake\share\cmake-3.30\Modules\FindPackageMessage.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-Initialize.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-C.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC-CXX.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows-MSVC.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\Windows.cmake;E:\Cmake\share\cmake-3.30\Modules\Platform\WindowsPaths.cmake;E:\OpenCV\opencv\build\OpenCVConfig-version.cmake;E:\OpenCV\opencv\build\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVConfig.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-debug.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules-release.cmake;E:\OpenCV\opencv\build\x64\vc16\lib\OpenCVModules.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeCXXCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeRCCompiler.cmake;E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\3.30.0-rc1\CMakeSystem.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">E:\PETlab\图形\hw\HHMM\Hw2\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="E:\PETlab\图形\hw\HHMM\Hw2\main.cpp" />
    <ClInclude Include="E:\PETlab\图形\hw\HHMM\Hw2\rasterizer.hpp" />
    <ClCompile Include="E:\PETlab\图形\hw\HHMM\Hw2\rasterizer.cpp" />
    <ClInclude Include="E:\PETlab\图形\hw\HHMM\Hw2\global.hpp" />
    <ClInclude Include="E:\PETlab\图形\hw\HHMM\Hw2\Triangle.hpp" />
    <ClCompile Include="E:\PETlab\图形\hw\HHMM\Hw2\Triangle.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="E:\PETlab\图形\hw\HHMM\Hw2\build\ZERO_CHECK.vcxproj">
      <Project>{CA219BE4-62FE-3836-9AC6-4B4E98F41156}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>