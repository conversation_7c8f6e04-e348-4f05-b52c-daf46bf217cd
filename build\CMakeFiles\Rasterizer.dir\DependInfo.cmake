
# Consider dependencies only in project.
set(CMAKE_DEPENDS_IN_PROJECT_ONLY OFF)

# The set of languages for which implicit dependencies are needed:
set(CMAKE_DEPENDS_LANGUAGES
  )

# The set of dependency files which are needed:
set(CMAKE_DEPENDS_DEPENDENCY_FILES
  "D:/pet/CODE/图形/HHMM/Hw3/Texture.cpp" "CMakeFiles/Rasterizer.dir/Texture.cpp.obj" "gcc" "CMakeFiles/Rasterizer.dir/Texture.cpp.obj.d"
  "D:/pet/CODE/图形/HHMM/Hw3/Triangle.cpp" "CMakeFiles/Rasterizer.dir/Triangle.cpp.obj" "gcc" "CMakeFiles/Rasterizer.dir/Triangle.cpp.obj.d"
  "D:/pet/CODE/图形/HHMM/Hw3/main.cpp" "CMakeFiles/Rasterizer.dir/main.cpp.obj" "gcc" "CMakeFiles/Rasterizer.dir/main.cpp.obj.d"
  "D:/pet/CODE/图形/HHMM/Hw3/rasterizer.cpp" "CMakeFiles/Rasterizer.dir/rasterizer.cpp.obj" "gcc" "CMakeFiles/Rasterizer.dir/rasterizer.cpp.obj.d"
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_LINKED_INFO_FILES
  )

# Targets to which this target links which contain Fortran sources.
set(CMAKE_Fortran_TARGET_FORWARD_LINKED_INFO_FILES
  )

# Fortran module output directory.
set(CMAKE_Fortran_TARGET_MODULE_DIR "")
